# main.py
from transformers import AutoModelForCausalLM, AutoTokenizer

def main():
    # 指定模型路径
    model_path = "./model"
    
    # 加载分词器和模型
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(model_path)

    # 输入提示文本
    prompt = "你好，请问有什么可以帮助你的吗？"
    
    # 对输入进行编码
    inputs = tokenizer(prompt, return_tensors="pt")
    
    # 生成文本
    outputs = model.generate(**inputs, max_length=100, num_return_sequences=1)
    
    # 解码输出
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    print("生成的文本：")
    print(generated_text)

if __name__ == "__main__":
    main()