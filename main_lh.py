# main_lh.py - CPU优化版本
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import gc

def main():
    # 指定模型路径
    model_path = "./model"

    print("正在加载CPU优化模型，请稍候...")

    # 加载分词器
    tokenizer = AutoTokenizer.from_pretrained(model_path)

    # CPU环境下的优化配置
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float32,              # CPU上使用float32更稳定
        low_cpu_mem_usage=True,                 # 启用低CPU内存使用模式
        trust_remote_code=True,                 # 信任远程代码
        device_map="cpu"                        # 明确指定使用CPU
    )

    print("模型加载完成！")
    print("提示：在CPU环境下，模型推理可能较慢，请耐心等待。")

    # 交互式对话循环
    while True:
        # 获取用户输入
        prompt = input("\n请输入您的问题（输入'quit'退出）: ")

        if prompt.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break

        if not prompt.strip():
            continue

        try:
            print("正在生成回复（CPU模式，请耐心等待）...")

            # 对输入进行编码
            inputs = tokenizer(prompt, return_tensors="pt")

            # CPU环境下的优化生成参数
            with torch.no_grad():  # 禁用梯度计算以节省内存
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=256,           # 减少最大token数以提高速度
                    temperature=0.8,              # 稍微提高随机性
                    do_sample=True,               # 启用采样
                    top_p=0.85,                   # nucleus采样
                    top_k=40,                     # 减少top-k以提高速度
                    repetition_penalty=1.05,      # 轻微重复惩罚
                    pad_token_id=tokenizer.eos_token_id,
                    use_cache=True,               # 启用缓存
                    num_beams=1                   # 使用贪婪搜索提高速度
                )

            # 解码输出（只获取新生成的部分）
            generated_text = tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            print("\n模型回复：")
            print(generated_text)

            # 清理内存
            del outputs
            gc.collect()

        except Exception as e:
            print(f"生成过程中出现错误: {e}")
            print("请尝试重新输入或检查模型配置。")
            # 清理内存
            gc.collect()

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    print("==================\n")

if __name__ == "__main__":
    # 显示系统信息
    check_system_info()
    
    # 运行主程序
    main()
