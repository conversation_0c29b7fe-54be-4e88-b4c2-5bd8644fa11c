# main_lh.py - GPU优化版本
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import gc
import os

def main():
    # 指定模型路径
    model_path = "./model"

    # 检查CUDA可用性并设置设备
    if torch.cuda.is_available():
        device = "cuda"
        print(f"使用GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

        # 清理GPU缓存
        torch.cuda.empty_cache()

        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
    else:
        device = "cpu"
        print("使用CPU模式")

    print("正在加载模型，请稍候...")

    try:
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True
        )

        # 尝试多种加载策略
        model = None
        loading_strategies = [
            # 策略1: 纯CPU加载（最稳定）
            {
                "name": "纯CPU模式",
                "config": {
                    "dtype": torch.float32,
                    "device_map": "cpu",
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True
                }
            },
            # 策略2: CPU-GPU混合（如果有足够内存）
            {
                "name": "CPU-GPU混合模式",
                "config": {
                    "dtype": torch.float16,
                    "device_map": "auto",
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True,
                    "max_memory": {0: "4GB", "cpu": "8GB"}
                }
            } if device == "cuda" else None,
            # 策略3: 纯GPU（如果模型较小）
            {
                "name": "纯GPU模式",
                "config": {
                    "dtype": torch.float16,
                    "device_map": {"": 0},
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True
                }
            } if device == "cuda" else None
        ]

        # 过滤掉None策略
        loading_strategies = [s for s in loading_strategies if s is not None]

        # 尝试每种策略
        for i, strategy in enumerate(loading_strategies):
            try:
                print(f"尝试策略 {i+1}: {strategy['name']}")
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    **strategy['config']
                )
                print(f"✓ {strategy['name']} 加载成功！")

                # 更新设备信息
                if "cpu" in str(strategy['config'].get('device_map', '')):
                    device = "cpu"
                elif device == "cuda":
                    device = "cuda"

                break

            except Exception as e:
                print(f"✗ {strategy['name']} 失败: {str(e)[:100]}...")
                if model is not None:
                    del model
                    model = None
                if device == "cuda":
                    torch.cuda.empty_cache()
                gc.collect()
                continue

        if model is None:
            print("所有加载策略都失败了！")
            return

    except Exception as e:
        print(f"初始化过程出错: {e}")
        return

    print("模型加载完成！")
    print(f"当前使用设备: {device}")

    # 交互式对话循环
    while True:
        # 获取用户输入
        prompt = input("\n请输入您的问题（输入'quit'退出）: ")

        if prompt.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break

        if not prompt.strip():
            continue

        try:
            print("正在生成回复...")

            # 对输入进行编码
            inputs = tokenizer(prompt, return_tensors="pt")

            # 将输入移动到正确的设备
            if device == "cuda":
                inputs = {k: v.to(device) for k, v in inputs.items()}

            # 生成文本
            with torch.no_grad():  # 禁用梯度计算以节省内存
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=200,           # 适中的token数
                    temperature=0.7,              # 控制随机性
                    do_sample=True,               # 启用采样
                    top_p=0.9,                    # nucleus采样
                    top_k=50,                     # top-k采样
                    repetition_penalty=1.1,       # 重复惩罚
                    pad_token_id=tokenizer.eos_token_id if tokenizer.eos_token_id else tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    use_cache=True                # 启用缓存
                )

            # 解码输出（只获取新生成的部分）
            generated_text = tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            print("\n模型回复：")
            print(generated_text)

            # 清理内存
            del outputs, inputs
            if device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()

        except torch.cuda.OutOfMemoryError:
            print("GPU内存不足！尝试清理缓存...")
            torch.cuda.empty_cache()
            gc.collect()
            print("请尝试输入更短的问题。")

        except Exception as e:
            print(f"生成过程中出现错误: {e}")
            print("请尝试重新输入或检查模型配置。")
            # 清理内存
            if device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()

def check_model_info(model_path):
    """检查模型信息"""
    try:
        from transformers import AutoConfig
        config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)

        print("=== 模型信息 ===")
        if hasattr(config, 'model_type'):
            print(f"模型类型: {config.model_type}")
        if hasattr(config, 'hidden_size'):
            print(f"隐藏层大小: {config.hidden_size}")
        if hasattr(config, 'num_hidden_layers'):
            print(f"层数: {config.num_hidden_layers}")
        if hasattr(config, 'vocab_size'):
            print(f"词汇表大小: {config.vocab_size}")
        print("==================\n")
    except Exception as e:
        print(f"无法获取模型信息: {e}\n")

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    print("==================\n")

if __name__ == "__main__":
    # 显示系统信息
    check_system_info()

    # 检查模型信息
    check_model_info("./model")

    # 运行主程序
    main()
