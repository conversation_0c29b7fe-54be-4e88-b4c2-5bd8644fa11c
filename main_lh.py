# main_lh.py - FP8量化版本
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
import torch
import gc
import os
import psutil
import sys

def main():
    # 指定模型路径
    model_path = "./model"

    # 检查CUDA可用性并设置设备
    if torch.cuda.is_available():
        device = "cuda"
        print(f"使用GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

        # 清理GPU缓存
        torch.cuda.empty_cache()

        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
    else:
        device = "cpu"
        print("使用CPU模式")

    print("正在加载模型，请稍候...")

    try:
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True
        )

        # 检查系统资源
        check_system_resources()

        # 检查模型配置
        config = check_model_info(model_path)
        if config is None:
            print("无法获取模型配置，停止加载")
            return

        # 根据模型大小和系统资源决定加载策略
        memory = psutil.virtual_memory()
        available_gb = memory.available / 1024**3

        # 估算模型大小
        total_params, memory_fp32, memory_fp16, memory_int8, memory_int4, memory_fp8 = estimate_model_size(config)

        print("=== 加载策略选择 ===")
        if total_params and total_params > 50000:  # 超过50B参数
            print(f"⚠️  检测到大型模型 ({total_params/1000:.1f}B 参数)")
            print("建议使用量化模式以减少内存使用")

        # 尝试多种加载策略（从最激进的量化开始）
        model = None
        loading_strategies = []

        # 根据可用内存和CUDA支持选择策略
        if device == "cuda":
            # GPU策略：优先使用量化
            loading_strategies.extend([
                {
                    "name": "4bit量化 (GPU)",
                    "config": {
                        "quantization_config": BitsAndBytesConfig(
                            load_in_4bit=True,
                            bnb_4bit_compute_dtype=torch.float16,
                            bnb_4bit_use_double_quant=True,
                            bnb_4bit_quant_type="nf4",
                            llm_int8_enable_fp32_cpu_offload=True
                        ),
                        "device_map": "auto",
                        "trust_remote_code": True,
                        "low_cpu_mem_usage": True,
                        "max_memory": {0: "6GB", "cpu": "16GB"}
                    }
                },
                {
                    "name": "8bit量化 (GPU)",
                    "config": {
                        "quantization_config": BitsAndBytesConfig(
                            load_in_8bit=True,
                            llm_int8_enable_fp32_cpu_offload=True
                        ),
                        "device_map": "auto",
                        "trust_remote_code": True,
                        "low_cpu_mem_usage": True,
                        "max_memory": {0: "6GB", "cpu": "16GB"}
                    }
                }
            ])

        # CPU策略：使用不同精度
        loading_strategies.extend([
            {
                "name": "FP16 (CPU)",
                "config": {
                    "dtype": torch.float16,
                    "device_map": "cpu",
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True
                }
            },
            {
                "name": "FP32 (CPU)",
                "config": {
                    "dtype": torch.float32,
                    "device_map": "cpu",
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True
                }
            }
        ])

        # 过滤掉None策略
        loading_strategies = [s for s in loading_strategies if s is not None]

        # 尝试每种策略
        for i, strategy in enumerate(loading_strategies):
            try:
                print(f"尝试策略 {i+1}: {strategy['name']}")
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    **strategy['config']
                )
                print(f"✓ {strategy['name']} 加载成功！")

                # 更新设备信息
                if "cpu" in str(strategy['config'].get('device_map', '')):
                    device = "cpu"
                elif device == "cuda":
                    device = "cuda"

                break

            except Exception as e:
                print(f"✗ {strategy['name']} 失败: {str(e)[:100]}...")
                if model is not None:
                    del model
                    model = None
                if device == "cuda":
                    torch.cuda.empty_cache()
                gc.collect()
                continue

        if model is None:
            print("所有加载策略都失败了！")
            return

    except Exception as e:
        print(f"初始化过程出错: {e}")
        return

    print("模型加载完成！")
    print(f"当前使用设备: {device}")

    # 交互式对话循环
    while True:
        # 获取用户输入
        prompt = input("\n请输入您的问题（输入'quit'退出）: ")

        if prompt.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break

        if not prompt.strip():
            continue

        try:
            print("正在生成回复...")

            # 对输入进行编码
            inputs = tokenizer(prompt, return_tensors="pt")

            # 将输入移动到正确的设备
            if device == "cuda":
                inputs = {k: v.to(device) for k, v in inputs.items()}

            # 生成文本
            with torch.no_grad():  # 禁用梯度计算以节省内存
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=200,           # 适中的token数
                    temperature=0.7,              # 控制随机性
                    do_sample=True,               # 启用采样
                    top_p=0.9,                    # nucleus采样
                    top_k=50,                     # top-k采样
                    repetition_penalty=1.1,       # 重复惩罚
                    pad_token_id=tokenizer.eos_token_id if tokenizer.eos_token_id else tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    use_cache=True                # 启用缓存
                )

            # 解码输出（只获取新生成的部分）
            generated_text = tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            print("\n模型回复：")
            print(generated_text)

            # 清理内存
            del outputs, inputs
            if device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()

        except torch.cuda.OutOfMemoryError:
            print("GPU内存不足！尝试清理缓存...")
            torch.cuda.empty_cache()
            gc.collect()
            print("请尝试输入更短的问题。")

        except Exception as e:
            print(f"生成过程中出现错误: {e}")
            print("请尝试重新输入或检查模型配置。")
            # 清理内存
            if device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()

def estimate_model_size(config):
    """估算模型大小"""
    try:
        # 基本参数估算
        hidden_size = getattr(config, 'hidden_size', 4096)
        num_layers = getattr(config, 'num_hidden_layers', 32)
        vocab_size = getattr(config, 'vocab_size', 50000)

        # 粗略估算参数数量（单位：百万）
        # 这是一个简化的估算
        embedding_params = vocab_size * hidden_size
        layer_params = num_layers * (
            # 自注意力层
            4 * hidden_size * hidden_size +  # Q, K, V, O
            # 前馈网络层
            8 * hidden_size * hidden_size    # 通常是4倍隐藏层大小
        )

        total_params = (embedding_params + layer_params) / 1_000_000  # 转换为百万

        # 估算内存需求（GB）
        # float32: 4 bytes per parameter
        # float16: 2 bytes per parameter
        # int8: 1 byte per parameter
        # int4: 0.5 bytes per parameter
        # fp8: ~1 byte per parameter (包括量化开销)
        memory_fp32 = total_params * 4 / 1024  # GB
        memory_fp16 = total_params * 2 / 1024  # GB
        memory_int8 = total_params * 1 / 1024  # GB
        memory_int4 = total_params * 0.5 / 1024  # GB
        memory_fp8 = total_params * 1 / 1024  # GB

        return total_params, memory_fp32, memory_fp16, memory_int8, memory_int4, memory_fp8
    except:
        return None, None, None, None, None, None

def check_system_resources():
    """检查系统资源"""
    print("=== 系统资源 ===")

    # 检查内存
    memory = psutil.virtual_memory()
    print(f"系统内存: {memory.total / 1024**3:.1f} GB")
    print(f"可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")

    # 检查GPU内存
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            total_memory = torch.cuda.get_device_properties(i).total_memory
            allocated = torch.cuda.memory_allocated(i)
            cached = torch.cuda.memory_reserved(i)
            free = total_memory - cached

            print(f"GPU {i} 总内存: {total_memory / 1024**3:.1f} GB")
            print(f"GPU {i} 已分配: {allocated / 1024**3:.1f} GB")
            print(f"GPU {i} 已缓存: {cached / 1024**3:.1f} GB")
            print(f"GPU {i} 可用: {free / 1024**3:.1f} GB")

    print("==================\n")

def check_model_info(model_path):
    """检查模型信息"""
    try:
        from transformers import AutoConfig
        config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)

        print("=== 模型信息 ===")
        if hasattr(config, 'model_type'):
            print(f"模型类型: {config.model_type}")
        if hasattr(config, 'hidden_size'):
            print(f"隐藏层大小: {config.hidden_size}")
        if hasattr(config, 'num_hidden_layers'):
            print(f"层数: {config.num_hidden_layers}")
        if hasattr(config, 'vocab_size'):
            print(f"词汇表大小: {config.vocab_size}")

        # 估算模型大小
        total_params, memory_fp32, memory_fp16, memory_int8, memory_int4, memory_fp8 = estimate_model_size(config)
        if total_params:
            print(f"估算参数量: {total_params:.1f}M ({total_params/1000:.1f}B)")
            print(f"估算内存需求:")
            print(f"  FP32: {memory_fp32:.1f} GB")
            print(f"  FP16: {memory_fp16:.1f} GB")
            print(f"  INT8: {memory_int8:.1f} GB")
            print(f"  INT4: {memory_int4:.1f} GB")
            print(f"  FP8:  {memory_fp8:.1f} GB")

            # 检查是否可能加载
            memory = psutil.virtual_memory()
            available_gb = memory.available / 1024**3

            print(f"\n内存兼容性检查 (可用: {available_gb:.1f}GB):")

            modes = [
                ("FP32", memory_fp32),
                ("FP16", memory_fp16),
                ("INT8", memory_int8),
                ("INT4", memory_int4),
                ("FP8", memory_fp8)
            ]

            for mode_name, memory_needed in modes:
                if memory_needed > available_gb:
                    print(f"❌ {mode_name}模式需要 {memory_needed:.1f}GB，超出可用内存")
                else:
                    print(f"✅ {mode_name}模式可能可行 ({memory_needed:.1f}GB < {available_gb:.1f}GB)")

        print("==================\n")
        return config
    except Exception as e:
        print(f"无法获取模型信息: {e}\n")
        return None

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    print("==================\n")

if __name__ == "__main__":
    # 显示系统信息
    check_system_info()

    # 运行主程序
    main()
