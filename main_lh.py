# main_lh.py - GPU优化版本
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import gc
import os

def main():
    # 指定模型路径
    model_path = "./model"

    # 检查CUDA可用性并设置设备
    if torch.cuda.is_available():
        device = "cuda"
        print(f"使用GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

        # 清理GPU缓存
        torch.cuda.empty_cache()

        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
    else:
        device = "cpu"
        print("使用CPU模式")

    print("正在加载模型，请稍候...")

    try:
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True
        )

        # 根据设备选择不同的加载策略
        if device == "cuda":
            # GPU配置 - 使用更保守的设置
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                dtype=torch.float16,                # 使用半精度节省内存
                device_map="auto",                  # 自动分配设备
                trust_remote_code=True,
                low_cpu_mem_usage=True,
                max_memory={0: "6GB"}               # 限制GPU内存使用
            )
        else:
            # CPU配置
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                dtype=torch.float32,
                device_map="cpu",
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )

    except Exception as e:
        print(f"模型加载失败: {e}")
        print("尝试使用更保守的设置...")

        # 备用加载方案
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                dtype=torch.float32,
                device_map="cpu",
                trust_remote_code=True
            )
            device = "cpu"
            print("已切换到CPU模式")
        except Exception as e2:
            print(f"备用加载也失败: {e2}")
            return

    print("模型加载完成！")
    print(f"当前使用设备: {device}")

    # 交互式对话循环
    while True:
        # 获取用户输入
        prompt = input("\n请输入您的问题（输入'quit'退出）: ")

        if prompt.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break

        if not prompt.strip():
            continue

        try:
            print("正在生成回复...")

            # 对输入进行编码
            inputs = tokenizer(prompt, return_tensors="pt")

            # 将输入移动到正确的设备
            if device == "cuda":
                inputs = {k: v.to(device) for k, v in inputs.items()}

            # 生成文本
            with torch.no_grad():  # 禁用梯度计算以节省内存
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=200,           # 适中的token数
                    temperature=0.7,              # 控制随机性
                    do_sample=True,               # 启用采样
                    top_p=0.9,                    # nucleus采样
                    top_k=50,                     # top-k采样
                    repetition_penalty=1.1,       # 重复惩罚
                    pad_token_id=tokenizer.eos_token_id if tokenizer.eos_token_id else tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    use_cache=True                # 启用缓存
                )

            # 解码输出（只获取新生成的部分）
            generated_text = tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )

            print("\n模型回复：")
            print(generated_text)

            # 清理内存
            del outputs, inputs
            if device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()

        except torch.cuda.OutOfMemoryError:
            print("GPU内存不足！尝试清理缓存...")
            torch.cuda.empty_cache()
            gc.collect()
            print("请尝试输入更短的问题。")

        except Exception as e:
            print(f"生成过程中出现错误: {e}")
            print("请尝试重新输入或检查模型配置。")
            # 清理内存
            if device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    print("==================\n")

if __name__ == "__main__":
    # 显示系统信息
    check_system_info()
    
    # 运行主程序
    main()
