# main_lh.py - 量化模型版本
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
import torch

def main():
    # 指定模型路径
    model_path = "./model"
    
    # 配置量化参数 - 使用4bit量化
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,                      # 启用4bit量化
        bnb_4bit_compute_dtype=torch.float16,   # 计算时使用float16
        bnb_4bit_use_double_quant=True,         # 使用双重量化
        bnb_4bit_quant_type="nf4"               # 使用NF4量化类型
    )
    
    print("正在加载量化模型，请稍候...")
    
    # 加载分词器
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # 加载量化模型
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=quantization_config,
        device_map="auto",                      # 自动分配设备
        torch_dtype=torch.float16,              # 使用半精度
        trust_remote_code=True                  # 信任远程代码
    )
    
    print("模型加载完成！")
    
    # 交互式对话循环
    while True:
        # 获取用户输入
        prompt = input("\n请输入您的问题（输入'quit'退出）: ")
        
        if prompt.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break
            
        if not prompt.strip():
            continue
            
        try:
            print("正在生成回复...")
            
            # 对输入进行编码
            inputs = tokenizer(prompt, return_tensors="pt")
            
            # 将输入移动到模型所在设备
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # 生成文本
            with torch.no_grad():  # 禁用梯度计算以节省内存
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=512,           # 最大新生成token数
                    temperature=0.7,              # 控制随机性
                    do_sample=True,               # 启用采样
                    top_p=0.9,                    # nucleus采样
                    top_k=50,                     # top-k采样
                    repetition_penalty=1.1,       # 重复惩罚
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # 解码输出（只获取新生成的部分）
            generated_text = tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )
            
            print("\n模型回复：")
            print(generated_text)
            
        except Exception as e:
            print(f"生成过程中出现错误: {e}")
            print("请尝试重新输入或检查模型配置。")

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"GPU {i} 内存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    print("==================\n")

if __name__ == "__main__":
    # 显示系统信息
    check_system_info()
    
    # 运行主程序
    main()
