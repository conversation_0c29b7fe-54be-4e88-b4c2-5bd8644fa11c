# main_fp8.py - FP8量化专用版本
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import gc
import os
import psutil

def check_fp8_support():
    """检查FP8支持"""
    print("=== FP8支持检查 ===")
    
    # 检查PyTorch版本
    torch_version = torch.__version__
    print(f"PyTorch版本: {torch_version}")
    
    # 检查CUDA和GPU
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"GPU: {gpu_name}")
        
        # 检查是否支持FP8 (需要H100或更新的GPU)
        if "H100" in gpu_name or "A100" in gpu_name:
            print("✅ GPU支持FP8量化")
            return True
        else:
            print("⚠️  当前GPU可能不完全支持FP8，但可以尝试")
            return True
    else:
        print("❌ 没有CUDA支持，FP8量化需要GPU")
        return False

def create_fp8_config():
    """创建FP8量化配置"""
    try:
        # 尝试使用torch的原生FP8支持
        if hasattr(torch, 'float8_e4m3fn'):
            print("使用PyTorch原生FP8支持")
            return {
                "torch_dtype": torch.float8_e4m3fn,
                "device_map": "auto",
                "trust_remote_code": True,
                "low_cpu_mem_usage": True
            }
        else:
            print("PyTorch版本不支持原生FP8，尝试其他方法")
            return None
    except Exception as e:
        print(f"FP8配置创建失败: {e}")
        return None

def main():
    model_path = "./model"
    
    print("=== FP8量化模型加载器 ===\n")
    
    # 检查FP8支持
    if not check_fp8_support():
        print("系统不支持FP8量化，请使用其他量化方法")
        return
    
    print("\n=== 系统资源 ===")
    memory = psutil.virtual_memory()
    print(f"可用内存: {memory.available / 1024**3:.1f} GB")
    
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory
        print(f"GPU内存: {gpu_memory / 1024**3:.1f} GB")
    
    print("\n=== 开始加载模型 ===")
    
    try:
        # 加载分词器
        print("加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        
        # 尝试不同的量化策略
        strategies = [
            {
                "name": "FP8量化 (实验性)",
                "config": create_fp8_config()
            },
            {
                "name": "BF16量化",
                "config": {
                    "torch_dtype": torch.bfloat16,
                    "device_map": "auto",
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True,
                    "max_memory": {0: "6GB", "cpu": "12GB"}
                }
            },
            {
                "name": "FP16量化",
                "config": {
                    "torch_dtype": torch.float16,
                    "device_map": "auto", 
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True,
                    "max_memory": {0: "6GB", "cpu": "12GB"}
                }
            }
        ]
        
        model = None
        for strategy in strategies:
            if strategy["config"] is None:
                continue
                
            try:
                print(f"\n尝试: {strategy['name']}")
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    **strategy["config"]
                )
                print(f"✅ {strategy['name']} 加载成功！")
                break
                
            except Exception as e:
                print(f"❌ {strategy['name']} 失败: {str(e)[:100]}...")
                if model is not None:
                    del model
                    model = None
                torch.cuda.empty_cache()
                gc.collect()
                continue
        
        if model is None:
            print("所有量化策略都失败了")
            return
            
        print(f"\n模型加载完成！")
        
        # 显示模型信息
        try:
            total_params = sum(p.numel() for p in model.parameters())
            print(f"模型参数数量: {total_params / 1e9:.1f}B")
            
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated() / 1024**3
                print(f"GPU内存使用: {allocated:.1f} GB")
        except:
            pass
        
        # 交互式对话
        print("\n=== 开始对话 ===")
        while True:
            prompt = input("\n请输入问题 (输入'quit'退出): ")
            
            if prompt.lower() in ['quit', 'exit', '退出']:
                break
                
            if not prompt.strip():
                continue
                
            try:
                print("生成中...")
                
                inputs = tokenizer(prompt, return_tensors="pt")
                if torch.cuda.is_available():
                    inputs = {k: v.cuda() for k, v in inputs.items()}
                
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=200,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                response = tokenizer.decode(
                    outputs[0][inputs['input_ids'].shape[1]:],
                    skip_special_tokens=True
                )
                
                print(f"\n回复: {response}")
                
                # 清理内存
                del outputs, inputs
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"生成失败: {e}")
                torch.cuda.empty_cache()
                
    except Exception as e:
        print(f"程序出错: {e}")
    
    finally:
        # 清理资源
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

if __name__ == "__main__":
    main()
