{"metadata": {"total_size": 41208353711}, "weight_map": {"lm_head.weight": "model-00009-of-00009.safetensors", "model.embed_tokens.weight": "model-00001-of-00009.safetensors", "model.layers.1.input_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.1.mlp.down_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.mlp.down_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.mlp.down_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.mlp.down_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.mlp.down_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.1.mlp.gate_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.mlp.gate_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.mlp.gate_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.mlp.gate_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.mlp.gate_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.1.mlp.up_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.mlp.up_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.mlp.up_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.mlp.up_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.mlp.up_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.1.post_attention_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.bias": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.o_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.o_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.o_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.o_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.o_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.bias": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.bias": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.weight": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.1.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.101.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.101.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.101.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.101.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.101.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.101.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.103.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.103.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.105.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.105.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.107.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.107.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.109.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.109.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.11.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.11.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.11.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.11.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.11.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.11.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.11.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.11.mlp.gate_proj.weight": "model-00001-of-00009.safetensors", "model.layers.11.mlp.gate_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.11.mlp.gate_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.11.mlp.gate_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.11.mlp.gate_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.11.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.11.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.11.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.11.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.11.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.11.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.11.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.11.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.11.self_attn.k_proj.bias": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.k_proj.weight": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.k_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.k_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.k_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.k_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.o_proj.weight": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.o_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.o_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.o_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.o_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.bias": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.weight": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.bias": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.weight": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.11.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.111.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.111.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.111.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.111.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.111.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.111.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.113.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.113.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.115.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.115.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.117.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.117.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.119.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.119.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.121.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.121.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.121.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.121.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.121.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.121.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.121.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.121.mlp.gate_proj.weight": "model-00006-of-00009.safetensors", "model.layers.121.mlp.gate_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.121.mlp.gate_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.121.mlp.gate_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.121.mlp.gate_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.121.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.121.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.121.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.121.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.121.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.121.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.121.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.121.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.121.self_attn.k_proj.bias": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.k_proj.weight": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.k_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.k_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.k_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.k_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.o_proj.weight": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.o_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.o_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.o_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.o_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.bias": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.weight": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.bias": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.weight": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.121.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.123.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.123.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.123.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.123.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.123.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.123.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.125.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.125.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.127.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.127.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.129.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.129.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.13.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.13.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.13.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.13.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.13.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.13.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.131.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.131.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.131.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.131.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.131.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.131.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.133.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.133.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.135.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.135.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.137.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.137.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.139.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.139.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.input_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.141.mlp.down_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.mlp.down_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.mlp.down_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.mlp.down_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.mlp.down_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.mlp.up_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.mlp.up_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.mlp.up_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.mlp.up_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.mlp.up_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.post_attention_layernorm.weight": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.141.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.143.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.143.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.143.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.143.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.143.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.143.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.143.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.143.mlp.gate_proj.weight": "model-00007-of-00009.safetensors", "model.layers.143.mlp.gate_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.143.mlp.gate_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.143.mlp.gate_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.143.mlp.gate_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.143.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.143.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.143.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.143.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.143.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.143.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.143.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.143.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.143.self_attn.k_proj.bias": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.k_proj.weight": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.k_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.k_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.k_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.k_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.o_proj.weight": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.o_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.o_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.o_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.o_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.bias": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.weight": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.bias": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.weight": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.weight.absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.weight.nested_absmax": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.weight.nested_quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.weight.quant_map": "model-00007-of-00009.safetensors", "model.layers.143.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00007-of-00009.safetensors", "model.layers.145.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.145.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.145.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.145.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.145.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.145.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.147.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.147.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.149.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.149.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.15.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.15.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.15.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.15.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.15.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.15.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.151.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.151.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.151.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.151.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.151.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.151.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.153.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.153.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.155.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.155.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.157.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.157.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.input_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.159.mlp.down_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.mlp.down_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.mlp.down_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.mlp.down_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.mlp.down_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.mlp.gate_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.mlp.gate_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.mlp.gate_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.mlp.gate_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.mlp.gate_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.mlp.up_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.mlp.up_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.mlp.up_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.mlp.up_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.mlp.up_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.post_attention_layernorm.weight": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.bias": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.o_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.o_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.o_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.o_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.o_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.bias": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.bias": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.weight": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.weight.absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.weight.nested_absmax": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.weight.nested_quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.weight.quant_map": "model-00008-of-00009.safetensors", "model.layers.159.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00008-of-00009.safetensors", "model.layers.17.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.17.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.17.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.17.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.17.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.17.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.19.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.19.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.21.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.21.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.23.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.23.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.25.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.25.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.27.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.27.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.29.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.29.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.3.input_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.3.mlp.down_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.mlp.down_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.mlp.down_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.mlp.down_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.mlp.down_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.3.mlp.gate_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.mlp.gate_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.mlp.gate_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.mlp.gate_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.mlp.gate_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.3.mlp.up_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.mlp.up_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.mlp.up_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.mlp.up_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.mlp.up_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.3.post_attention_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.bias": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.o_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.o_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.o_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.o_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.o_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.bias": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.bias": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.weight": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.3.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.31.input_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.31.mlp.down_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.mlp.down_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.mlp.down_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.mlp.down_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.mlp.down_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.31.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.31.mlp.up_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.mlp.up_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.mlp.up_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.mlp.up_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.mlp.up_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.31.post_attention_layernorm.weight": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.31.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.33.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.33.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.33.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.33.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.33.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.33.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.33.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.33.mlp.gate_proj.weight": "model-00002-of-00009.safetensors", "model.layers.33.mlp.gate_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.33.mlp.gate_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.33.mlp.gate_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.33.mlp.gate_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.33.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.33.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.33.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.33.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.33.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.33.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.33.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.33.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.33.self_attn.k_proj.bias": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.k_proj.weight": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.k_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.k_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.k_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.k_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.o_proj.weight": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.o_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.o_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.o_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.o_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.bias": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.weight": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.bias": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.weight": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.weight.absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.weight.nested_absmax": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.weight.nested_quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.weight.quant_map": "model-00002-of-00009.safetensors", "model.layers.33.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00002-of-00009.safetensors", "model.layers.35.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.35.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.35.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.35.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.35.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.35.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.37.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.37.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.39.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.39.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.41.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.41.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.43.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.43.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.45.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.45.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.47.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.47.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.49.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.49.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.5.input_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.5.mlp.down_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.mlp.down_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.mlp.down_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.mlp.down_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.mlp.down_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.5.mlp.gate_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.mlp.gate_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.mlp.gate_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.mlp.gate_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.mlp.gate_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.5.mlp.up_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.mlp.up_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.mlp.up_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.mlp.up_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.mlp.up_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.5.post_attention_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.bias": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.o_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.o_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.o_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.o_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.o_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.bias": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.bias": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.weight": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.5.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.51.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.51.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.51.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.51.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.51.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.51.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.input_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.53.mlp.down_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.mlp.down_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.mlp.down_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.mlp.down_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.mlp.down_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.mlp.up_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.mlp.up_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.mlp.up_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.mlp.up_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.mlp.up_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.post_attention_layernorm.weight": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.53.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.55.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.55.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.55.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.55.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.55.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.55.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.55.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.55.mlp.gate_proj.weight": "model-00003-of-00009.safetensors", "model.layers.55.mlp.gate_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.55.mlp.gate_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.55.mlp.gate_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.55.mlp.gate_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.55.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.55.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.55.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.55.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.55.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.55.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.55.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.55.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.55.self_attn.k_proj.bias": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.k_proj.weight": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.k_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.k_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.k_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.k_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.o_proj.weight": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.o_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.o_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.o_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.o_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.bias": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.weight": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.bias": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.weight": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.weight.absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.weight.nested_absmax": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.weight.nested_quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.weight.quant_map": "model-00003-of-00009.safetensors", "model.layers.55.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00003-of-00009.safetensors", "model.layers.57.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.57.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.57.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.57.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.57.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.57.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.59.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.59.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.61.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.61.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.63.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.63.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.65.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.65.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.67.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.67.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.69.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.69.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.7.input_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.7.mlp.down_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.mlp.down_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.mlp.down_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.mlp.down_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.mlp.down_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.7.mlp.gate_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.mlp.gate_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.mlp.gate_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.mlp.gate_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.mlp.gate_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.7.mlp.up_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.mlp.up_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.mlp.up_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.mlp.up_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.mlp.up_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.7.post_attention_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.bias": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.o_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.o_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.o_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.o_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.o_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.bias": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.bias": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.weight": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.7.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.71.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.71.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.71.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.71.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.71.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.71.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.73.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.73.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.input_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.75.mlp.down_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.mlp.down_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.mlp.down_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.mlp.down_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.mlp.down_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.mlp.up_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.mlp.up_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.mlp.up_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.mlp.up_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.mlp.up_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.post_attention_layernorm.weight": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.75.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.77.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.77.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.77.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.77.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.77.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.77.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.77.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.77.mlp.gate_proj.weight": "model-00004-of-00009.safetensors", "model.layers.77.mlp.gate_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.77.mlp.gate_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.77.mlp.gate_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.77.mlp.gate_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.77.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.77.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.77.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.77.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.77.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.77.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.77.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.77.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.77.self_attn.k_proj.bias": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.k_proj.weight": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.k_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.k_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.k_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.k_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.o_proj.weight": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.o_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.o_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.o_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.o_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.bias": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.weight": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.bias": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.weight": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.weight.absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.weight.nested_absmax": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.weight.nested_quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.weight.quant_map": "model-00004-of-00009.safetensors", "model.layers.77.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00004-of-00009.safetensors", "model.layers.79.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.79.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.79.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.79.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.79.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.79.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.81.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.81.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.83.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.83.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.85.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.85.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.87.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.87.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.89.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.89.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.9.input_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.9.mlp.down_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.mlp.down_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.mlp.down_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.mlp.down_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.mlp.down_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.9.mlp.gate_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.mlp.gate_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.mlp.gate_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.mlp.gate_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.mlp.gate_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.9.mlp.up_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.mlp.up_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.mlp.up_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.mlp.up_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.mlp.up_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.9.post_attention_layernorm.weight": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.bias": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.o_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.o_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.o_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.o_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.o_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.bias": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.bias": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.weight": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.weight.absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.weight.nested_absmax": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.weight.nested_quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.weight.quant_map": "model-00001-of-00009.safetensors", "model.layers.9.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00001-of-00009.safetensors", "model.layers.91.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.91.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.91.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.91.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.91.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.91.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.93.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.93.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.95.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.95.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.input_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.97.mlp.down_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.mlp.down_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.mlp.down_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.mlp.down_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.mlp.down_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.mlp.up_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.mlp.up_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.mlp.up_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.mlp.up_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.mlp.up_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.post_attention_layernorm.weight": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.97.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.99.input_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.99.mlp.down_proj.weight": "model-00006-of-00009.safetensors", "model.layers.99.mlp.down_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.99.mlp.down_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.99.mlp.down_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.99.mlp.down_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.99.mlp.down_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.99.mlp.gate_proj.weight": "model-00005-of-00009.safetensors", "model.layers.99.mlp.gate_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.99.mlp.gate_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.99.mlp.gate_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.99.mlp.gate_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.99.mlp.gate_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.99.mlp.up_proj.weight": "model-00006-of-00009.safetensors", "model.layers.99.mlp.up_proj.weight.absmax": "model-00006-of-00009.safetensors", "model.layers.99.mlp.up_proj.weight.nested_absmax": "model-00006-of-00009.safetensors", "model.layers.99.mlp.up_proj.weight.nested_quant_map": "model-00006-of-00009.safetensors", "model.layers.99.mlp.up_proj.weight.quant_map": "model-00006-of-00009.safetensors", "model.layers.99.mlp.up_proj.weight.quant_state.bitsandbytes__fp4": "model-00006-of-00009.safetensors", "model.layers.99.post_attention_layernorm.weight": "model-00006-of-00009.safetensors", "model.layers.99.self_attn.k_proj.bias": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.k_proj.weight": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.k_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.k_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.k_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.k_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.k_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.o_proj.weight": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.o_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.o_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.o_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.o_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.o_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.bias": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.weight": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.q_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.bias": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.weight": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.weight.absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.weight.nested_absmax": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.weight.nested_quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.weight.quant_map": "model-00005-of-00009.safetensors", "model.layers.99.self_attn.v_proj.weight.quant_state.bitsandbytes__fp4": "model-00005-of-00009.safetensors", "model.norm.weight": "model-00008-of-00009.safetensors"}}